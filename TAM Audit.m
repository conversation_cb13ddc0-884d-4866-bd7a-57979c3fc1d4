let
    // 添加错误处理的源文件获取
    SourceFiles = try Folder.Files("C:\WorkFileDocuments\PitData") otherwise #table({"Name"}, {}),
    FileCheck = if Table.RowCount(SourceFiles) = 0 then error "未找到源文件" else SourceFiles,
    LatestFile = Table.FirstN(Table.Sort(FileCheck, {{"Date modified", Order.Descending}}), 1),
    
    ExcelContent = Table.AddColumn(
        Table.SelectColumns(LatestFile, {"Content"}), 
        "WorkbookData", 
        each Excel.Workbook([Content])
    ),
    
    SheetsOnly = Table.SelectRows(
        Table.ExpandTableColumn(ExcelContent, "WorkbookData", {"Name", "Data", "Item", "Kind", "Hidden"}), 
        each ([Kind] = "Sheet")
    ),
    
    // 动态生成列名
    DynamicColumns = List.Transform(List.Numbers(1, 59), each "Column" & Text.From(_)),
    ExpandedData = Table.ExpandTableColumn(
        Table.SelectColumns(SheetsOnly, {"Data"}), 
        "Data", 
        DynamicColumns, 
        DynamicColumns
    ),
    
    PromotedHeaders = Table.PromoteHeaders(ExpandedData, [PromoteAllScalars=true]),
    
    StaticColumns = {"#Customer Item", "Supplier", "Customer Site", "CFG", "Customer Item Description", "Buyer Name", "CSR", "GSM Name", "Buy To Backlog", "Commit Threshold", "Data Measure"},
    
    TypedData = Table.TransformColumnTypes(PromotedHeaders, 
        {{"#Customer Item", type text}, {"Supplier", type text}, {"Customer Site", type text}, {"CFG", type text}, {"Customer Item Description", type text}, {"Buyer Name", type text}, {"CSR", type any}, {"GSM Name", type text}, {"Buy To Backlog", type text}, {"Commit Threshold", Int64.Type}, {"Data Measure", type text}} 
        & List.Transform(List.Skip(Table.ColumnNames(PromotedHeaders), 11), each {_, type number})),
    
    FilteredRows = Table.SelectRows(TypedData, each ([Data Measure] = "Released Forecast")),
    DateColumns = List.Skip(Table.ColumnNames(FilteredRows), 11),
    
    DataWithTotalSum = Table.AddColumn(FilteredRows, "TotalSum", each List.Sum(Record.ToList(Record.SelectFields(_, DateColumns)))),
    CleanedData = Table.RemoveColumns(DataWithTotalSum, DateColumns),
    
    // 分组计算
    GroupedData = Table.Group(CleanedData, {"Customer Site", "#Customer Item"}, {{"TotalSumGroup", each List.Sum([TotalSum]), type nullable number}}),
    
    // 合并分组结果
    MergedWithGroups = Table.NestedJoin(CleanedData, {"Customer Site", "#Customer Item"}, GroupedData, {"Customer Site", "#Customer Item"}, "GroupedData", JoinKind.LeftOuter),
    ExpandedGroups = Table.ExpandTableColumn(MergedWithGroups, "GroupedData", {"TotalSumGroup"}),
    
    // 计算比例
    DataWithProportion = Table.AddColumn(ExpandedGroups, "Proportion", each if [TotalSumGroup] = null then null else [TotalSum] / [TotalSumGroup], type number),
    
    // 创建站点映射函数
    MapCustomerSite = (siteName as text) as text =>
        let
            mapping = [
                T2_COMCHE = "COMPALChina",
                T2_COMHAN = "COMPALVietnam", 
                T2_COMKUN = "COMPALChina",
                T2_WISCHE = "WISTRONChina",
                T2_WISHNM = "WISTRONVietnam"
            ]
        in
            Record.FieldOrDefault(mapping, siteName, siteName),
    
    // 应用站点映射并重命名列
    MappedSites = Table.TransformColumns(DataWithProportion, {{"Customer Site", MapCustomerSite}}),
    RenamedSite = Table.RenameColumns(MappedSites, {{"Customer Site", "Customer Site2"}}),
    TypedProportion = Table.TransformColumnTypes(RenamedSite, {{"Proportion", Percentage.Type}}),
    
    // 创建合并列用于TAM连接
    DataWithMergedColumn = Table.AddColumn(TypedProportion, "MergedColumn", each Text.Combine({[Customer Site2], [Supplier], [#"#Customer Item"]}, ""), type text),
    
    // 安全的TAM表连接
    SafeTAMJoin = try Table.NestedJoin(DataWithMergedColumn, {"MergedColumn"}, TAM, {"Merged"}, "TAM", JoinKind.LeftOuter)
                  otherwise error "TAM表连接失败",

    // 动态获取TAM列
    TAMColumns = List.Select(
        Table.ColumnNames(TAM), 
        each Text.StartsWith(_, "Q") and Text.EndsWith(_, "TAM")
    ),

    ExpandedTAM = Table.ExpandTableColumn(SafeTAMJoin, "TAM", TAMColumns, TAMColumns),
    
    // 批量处理舍入
    RoundedProportion = Table.TransformColumns(ExpandedTAM, {{"Proportion", each Number.Round(_, 2), Percentage.Type}}),
    
    // 移除不需要的列
    CleanedColumns = Table.RemoveColumns(RoundedProportion, {"CFG","Customer Item Description", "CSR", "GSM Name", "Buy To Backlog", "Commit Threshold", "MergedColumn", "Data Measure"}),
    
    // 重新排列列
    ReorderedColumns = Table.ReorderColumns(CleanedColumns, {"Customer Site2", "#Customer Item", "Supplier", "Buyer Name", "TotalSum", "TotalSumGroup", "Proportion"} & TAMColumns),
    
    // 批量处理TAM列的舍入
    RoundedTAMColumns = Table.TransformColumns(
        ReorderedColumns, 
        List.Transform(TAMColumns, each {_, (value) => Number.Round(value, 2), Percentage.Type})
    ),
    
    // 简化Gap计算逻辑
    FinalResult = Table.AddColumn(RoundedTAMColumns, "Gap>2%", 
        each let
            proportion = [Proportion],
            tamValue = if List.Count(TAMColumns) > 0 then Record.Field(_, TAMColumns{0}) else null
        in
            if proportion = null or tamValue = null 
            then null 
            else Number.Abs(proportion - tamValue) > 0.02
    )
in
    FinalResult
